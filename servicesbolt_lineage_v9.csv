source_node,source_type,destination_node,destination_type,relationship
ServicesBolt,Folder,Application,File,CONTAINS
ServicesBolt,Folder,TriggerCollector,File,CONTAINS
ServicesBolt,Folder,Api,Folder,CONTAINS
Api,Folder,ALMConfigController,File,CONTAINS
Api,Folder,AlmController,File,CONTAINS
ServicesBolt,Folder,Request,Folder,CONTAINS
Request,Folder,ALMConfigReq,File,CONTAINS
Request,Folder,ALMToolReq,File,CONTAINS
ServicesBolt,Folder,Response,Folder,CONTAINS
Response,Folder,Authentication,File,CONTAINS
Response,Folder,DataResponse,File,CONTAINS
ServicesBolt,Folder,Service,Folder,CONTAINS
Service,Folder,ALMConfigService,File,CONTAINS
Service,Folder,ALMConfigServiceImplementation,File,CONTAINS
Service,Folder,AlmService,File,CONTAINS
Service,Folder,ALMServiceImplementation,File,CONTAINS
Service,Folder,ConfigurationSettingService,File,CONTAINS
Service,Folder,ConfigurationSettingServiceImplementation,File,CONTAINS
ServicesBolt,Folder,Util,Folder,CONTAINS
Util,Folder,DateUtil,File,CONTAINS
Application,File,Application,Class,DECLARES
TriggerCollector,File,TriggerCollector,Class,DECLARES
ALMConfigController,File,ALMConfigController,Class,DECLARES
AlmController,File,AlmController,Class,DECLARES
ALMConfigReq,File,ALMConfigReq,Class,DECLARES
ALMToolReq,File,ALMToolReq,Class,DECLARES
DataResponse,File,DataResponse,Class,DECLARES
ALMConfigServiceImplementation,File,ALMConfigServiceImplementation,Class,DECLARES
ALMServiceImplementation,File,ALMServiceImplementation,Class,DECLARES
ALMServiceImplementation,File,TransitionComparator,Class,DECLARES
ALMServiceImplementation,File,SprintComparatort,Class,DECLARES
ConfigurationSettingServiceImplementation,File,ConfigurationSettingServiceImplementation,Class,DECLARES
DateUtil,File,DateUtil,Class,DECLARES
Application,Class,configure,Method,DECLARES
Application,Class,passwordEncoder,Method,DECLARES
Application,Class,main,Method,DECLARES
TriggerCollector,Class,TriggerCollector.Logger,Variable,HAS_FIELD
TriggerCollector,Class,TriggerCollector.Ctx,Variable,HAS_FIELD
TriggerCollector,Class,getDataFromTools,Method,DECLARES
getDataFromTools,Method,TriggerCollector.Ctx,Variable,USES
getDataFromTools,Method,TriggerCollector.Portfolio,Variable,USES
getDataFromTools,Method,TriggerCollector.Data,Variable,USES
getDataFromTools,Method,TriggerCollector.Scheduler,Variable,USES
getDataFromTools,Method,TriggerCollector.Jobkey,Variable,USES
getDataFromTools,Method,TriggerCollector.Job,Variable,USES
getDataFromTools,Method,TriggerCollector.Joba,Variable,USES
getDataFromTools,Method,TriggerCollector.Trigger,Variable,USES
ALMConfigController,Class,ALMConfigController.Log,Variable,HAS_FIELD
ALMConfigController,Class,ALMConfigController.Almconfigservice,Variable,HAS_FIELD
ALMConfigController,Class,saveALMConfig,Method,DECLARES
ALMConfigController,Class,retrieveList,Method,DECLARES
ALMConfigController,Class,retrieveALMConfig,Method,DECLARES
ALMConfigController,Class,/Almconfig,Endpoint,DECLARES
ALMConfigController,Class,/Almconfigdetails,Endpoint,DECLARES
ALMConfigController,Class,/Almconfigdetailsconfig,Endpoint,DECLARES
AlmController,Class,AlmController.Service,Variable,HAS_FIELD
AlmController,Class,storyAgeing,Method,DECLARES
AlmController,Class,groomingTable,Method,DECLARES
AlmController,Class,delDuplicate,Method,DECLARES
AlmController,Class,getSprintProgressHome,Method,DECLARES
AlmController,Class,defectInsightData,Method,DECLARES
AlmController,Class,defectTrendAndClassification,Method,DECLARES
AlmController,Class,defectClassification,Method,DECLARES
AlmController,Class,getIssueBrakeUp,Method,DECLARES
AlmController,Class,getStoryProgress,Method,DECLARES
AlmController,Class,getDefectsSummaryHome,Method,DECLARES
AlmController,Class,getTaskRiskStoryPoint,Method,DECLARES
AlmController,Class,burndownCalculation,Method,DECLARES
AlmController,Class,getProductionSlippage,Method,DECLARES
AlmController,Class,getDefectDensity,Method,DECLARES
AlmController,Class,getDefectBacklog,Method,DECLARES
AlmController,Class,getDefectPareto,Method,DECLARES
AlmController,Class,getActiveSprints,Method,DECLARES
AlmController,Class,delAllIsues,Method,DECLARES
AlmController,Class,getMetricsDatas,Method,DECLARES
AlmController,Class,getAllTransitions,Method,DECLARES
AlmController,Class,getProjectMetrics,Method,DECLARES
AlmController,Class,getChangesItems,Method,DECLARES
AlmController,Class,getTransitionsData,Method,DECLARES
AlmController,Class,getIterationData,Method,DECLARES
AlmController,Class,getEffortData,Method,DECLARES
AlmController,Class,getProjectDetials,Method,DECLARES
AlmController,Class,getCurrentProjectDetials,Method,DECLARES
AlmController,Class,getCurrentIter,Method,DECLARES
AlmController,Class,getIterations,Method,DECLARES
AlmController,Class,getDefectCount,Method,DECLARES
AlmController,Class,getRelease,Method,DECLARES
AlmController,Class,getUnReleaseData,Method,DECLARES
getUnReleaseData,Method,AlmController.Response,Variable,USES
AlmController,Class,getDefects,Method,DECLARES
getDefects,Method,AlmController.Response,Variable,USES
AlmController,Class,getSlaData,Method,DECLARES
AlmController,Class,getAssigneeIssues,Method,DECLARES
AlmController,Class,getDateIterations,Method,DECLARES
AlmController,Class,getProdDefects,Method,DECLARES
getProdDefects,Method,AlmController.Response,Variable,USES
getAlmType,Method,AlmController.Almtype,Variable,USES
getAlmType,Method,AlmController.Config,Variable,USES
getAlmType,Method,AlmController.Metric,Variable,USES
getAlmType,Method,AlmController.Configuration1,Variable,USES
getAlmType,Method,AlmController.Metric1,Variable,USES
AlmController,Class,getAlmType,Method,DECLARES
AlmController,Class,getVelocityChart,Method,DECLARES
AlmController,Class,getIssueHierarchy,Method,DECLARES
getIssueHierarchy,Method,AlmController.Response,Variable,USES
AlmController,Class,getComponentWiseIssueHierarchy,Method,DECLARES
getComponentWiseIssueHierarchy,Method,AlmController.Response,Variable,USES
AlmController,Class,getComponentWiseVelocityChart,Method,DECLARES
getComponentWiseVelocityChart,Method,AlmController.Resp,Variable,USES
AlmController,Class,getComponontWiseSprintWiseStories,Method,DECLARES
getComponontWiseSprintWiseStories,Method,AlmController.Response,Variable,USES
AlmController,Class,getComponents,Method,DECLARES
getComponents,Method,AlmController.Response,Variable,USES
AlmController,Class,updateComponent,Method,DECLARES
AlmController,Class,saveEngScore,Method,DECLARES
AlmController,Class,getFeatureMetrics,Method,DECLARES
ALMConfigReq,Class,ALMConfigReq.Storyname,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Priorityname,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Projectname,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Defectname,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Releasename,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Taskname,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Closestate,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Newstate,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Progressstate,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Criticalpriority,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Highpriority,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Medpriority,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Lowpriority,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Tracksset,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Rejectionphase,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Reopenphase,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Testingphase,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Productionphase,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Personhours,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Timezone,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Velocityfields,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Environment,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Safeenabled,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Ccrlabel,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Cycletimestates,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Throughputstates,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Firstsprint,Variable,HAS_FIELD
ALMConfigReq,Class,ALMConfigReq.Trendtype,Variable,HAS_FIELD
ALMConfigReq,Class,getCcrLabel,Method,DECLARES
ALMConfigReq,Class,setCcrLabel,Method,DECLARES
setCcrLabel,Method,ALMConfigReq.Ccrlabel,Variable,USES
ALMConfigReq,Class,getCycleTimeStates,Method,DECLARES
ALMConfigReq,Class,setCycleTimeStates,Method,DECLARES
setCycleTimeStates,Method,ALMConfigReq.Cycletimestates,Variable,USES
ALMConfigReq,Class,getThroughputStates,Method,DECLARES
ALMConfigReq,Class,setThroughputStates,Method,DECLARES
setThroughputStates,Method,ALMConfigReq.Throughputstates,Variable,USES
ALMConfigReq,Class,getRejectionPhase,Method,DECLARES
ALMConfigReq,Class,setRejectionPhase,Method,DECLARES
setRejectionPhase,Method,ALMConfigReq.Rejectionphase,Variable,USES
ALMConfigReq,Class,getReopenPhase,Method,DECLARES
ALMConfigReq,Class,setReopenPhase,Method,DECLARES
setReopenPhase,Method,ALMConfigReq.Reopenphase,Variable,USES
ALMConfigReq,Class,getTestingPhase,Method,DECLARES
ALMConfigReq,Class,setTestingPhase,Method,DECLARES
setTestingPhase,Method,ALMConfigReq.Testingphase,Variable,USES
ALMConfigReq,Class,getProductionPhase,Method,DECLARES
ALMConfigReq,Class,setProductionPhase,Method,DECLARES
setProductionPhase,Method,ALMConfigReq.Productionphase,Variable,USES
ALMConfigReq,Class,getStoryName,Method,DECLARES
ALMConfigReq,Class,setStoryName,Method,DECLARES
setStoryName,Method,ALMConfigReq.Storyname,Variable,USES
ALMConfigReq,Class,getPriorityName,Method,DECLARES
ALMConfigReq,Class,setPriorityName,Method,DECLARES
setPriorityName,Method,ALMConfigReq.Priorityname,Variable,USES
ALMToolReq,Class,ALMToolReq.Almtype,Variable,HAS_FIELD
ALMToolReq,Class,getAlmType,Method,DECLARES
ALMToolReq,Class,setAlmType,Method,DECLARES
setAlmType,Method,ALMToolReq.Almtype,Variable,USES
Authentication,Class,Authentication.Authntication,Variable,HAS_FIELD
Authentication,Class,Authenticationofservice,Method,DECLARES
Auth,Class,Authentication.Response,Variable,HAS_FIELD
Auth,Class,Authentication.Authenticationstatus,Variable,HAS_FIELD
Auth,Class,Authenticate,Method,DECLARES
Authenticate,Method,Authentication.Feature,Variable,USES
Authenticate,Method,Authentication.Client,Variable,USES
Authenticate,Method,Authentication.Webtarget,Variable,USES
Authenticate,Method,Authentication.Invocationbuilder,Variable,USES
DataResponse,Class,DataResponse.Result,Variable,HAS_FIELD
DataResponse,Class,DataResponse.Lastupdated,Variable,HAS_FIELD
DataResponse,Class,getResult,Method,DECLARES
DataResponse,Class,getLastUpdated,Method,DECLARES
Almconfigservice,Interface,saveALMConfig,Method,DECLARES
Almconfigservice,Interface,retrieveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,ALMConfigServiceImplementation.Almconfigrepo,Variable,HAS_FIELD
ALMConfigServiceImplementation,Class,ALMConfigServiceImplementation.Log,Variable,HAS_FIELD
ALMConfigServiceImplementation,Class,saveALMConfig,Method,DECLARES
ALMConfigServiceImplementation,Class,retrieveALMConfig,Method,DECLARES
retrieveALMConfig,Method,ALMConfigServiceImplementation.Lastupdate,Variable,USES
retrieveALMConfig,Method,ALMConfigServiceImplementation.Result,Variable,USES
Almservice,Interface,getMetricDetails,Method,DECLARES
Almservice,Interface,getAllMetrics,Method,DECLARES
Almservice,Interface,getChangesItems,Method,DECLARES
Almservice,Interface,getTransitionsData,Method,DECLARES
Almservice,Interface,getIterationData,Method,DECLARES
Almservice,Interface,getEffortData,Method,DECLARES
Almservice,Interface,getProjectDetails,Method,DECLARES
Almservice,Interface,getDefectCounts,Method,DECLARES
Almservice,Interface,getCrtItr,Method,DECLARES
Almservice,Interface,getRelease,Method,DECLARES
Almservice,Interface,getUnReleaseData,Method,DECLARES
Almservice,Interface,getDefects,Method,DECLARES
Almservice,Interface,getSlaData,Method,DECLARES
Almservice,Interface,getAssigneeIssues,Method,DECLARES
Almservice,Interface,getDateIterations,Method,DECLARES
Almservice,Interface,getProdDefects,Method,DECLARES
Almservice,Interface,delDuplicate,Method,DECLARES
Almservice,Interface,getCurrentProjectDetails,Method,DECLARES
Almservice,Interface,delAllIssues,Method,DECLARES
Almservice,Interface,getAllTransitions,Method,DECLARES
Almservice,Interface,getComponentVelocity,Method,DECLARES
Almservice,Interface,getComponentsSprint,Method,DECLARES
Almservice,Interface,getIssueHierarchy,Method,DECLARES
Almservice,Interface,getComponentWiseIssueHierarchy,Method,DECLARES
Almservice,Interface,getComponents,Method,DECLARES
Almservice,Interface,updateComponentsOfTaskandSubtask,Method,DECLARES
Almservice,Interface,getFeatureMetrics,Method,DECLARES
Almservice,Interface,getSprintProgressHome,Method,DECLARES
Almservice,Interface,getDefectsSummaryHome,Method,DECLARES
Almservice,Interface,getTaskRisk,Method,DECLARES
Almservice,Interface,getActiveSprints,Method,DECLARES
Almservice,Interface,getIssueBrakeUp,Method,DECLARES
Almservice,Interface,getStoryProgress,Method,DECLARES
Almservice,Interface,burndownCalculation,Method,DECLARES
Almservice,Interface,getDefectInsightData,Method,DECLARES
Almservice,Interface,defectParetoCalculation,Method,DECLARES
Almservice,Interface,getProductionSlippage,Method,DECLARES
Almservice,Interface,getDefectDensity,Method,DECLARES
Almservice,Interface,getDefectBacklog,Method,DECLARES
Almservice,Interface,getDefectTrendAndClassification,Method,DECLARES
Almservice,Interface,getStoryAgeingData,Method,DECLARES
Almservice,Interface,getGroomingTable,Method,DECLARES
Almservice,Interface,getAllIterations,Method,DECLARES
Almservice,Interface,getDefectClassification,Method,DECLARES
Almservice,Interface,saveEngScore,Method,DECLARES
Almservice,Interface,getComponentVelocityChart,Method,DECLARES
Almservice,Interface,getComponentsSprintStories,Method,DECLARES
Almservice,Interface,getIssueHierarchyChart,Method,DECLARES
Almservice,Interface,getComponentWiseIssueHierarchyChart,Method,DECLARES
Almservice,Interface,getComponentsChart,Method,DECLARES
ALMServiceImplementation,Class,getMetricDetails,Method,DECLARES
ALMServiceImplementation,Class,getAllMetrics,Method,DECLARES
ALMServiceImplementation,Class,getChangesItems,Method,DECLARES
ALMServiceImplementation,Class,getTransitionsData,Method,DECLARES
ALMServiceImplementation,Class,getIterationData,Method,DECLARES
ALMServiceImplementation,Class,getEffortData,Method,DECLARES
ALMServiceImplementation,Class,getProjectDetails,Method,DECLARES
ALMServiceImplementation,Class,getFeatureMetrics,Method,DECLARES
ALMServiceImplementation,Class,populateAuthor,Method,DECLARES
ALMServiceImplementation,Class,getCrtItr,Method,DECLARES
ALMServiceImplementation,Class,getDefectCounts,Method,DECLARES
ALMServiceImplementation,Class,getRelease,Method,DECLARES
ALMServiceImplementation,Class,getUnReleaseData,Method,DECLARES
ALMServiceImplementation,Class,getDefects,Method,DECLARES
ALMServiceImplementation,Class,getProdDefects,Method,DECLARES
ALMServiceImplementation,Class,getDateIterations,Method,DECLARES
ALMServiceImplementation,Class,getSlaData,Method,DECLARES
ALMServiceImplementation,Class,getAssigneeIssues,Method,DECLARES
ALMServiceImplementation,Class,delDuplicate,Method,DECLARES
ALMServiceImplementation,Class,getCurrentProjectDetails,Method,DECLARES
ALMServiceImplementation,Class,delAllIssues,Method,DECLARES
ALMServiceImplementation,Class,getAllTransitions,Method,DECLARES
ALMServiceImplementation,Class,getComponentVelocity,Method,DECLARES
ALMServiceImplementation,Class,getComponentsSprint,Method,DECLARES
ALMServiceImplementation,Class,getIssueHierarchy,Method,DECLARES
ALMServiceImplementation,Class,getComponentList,Method,DECLARES
ALMServiceImplementation,Class,getComponentWiseIssueHierarchy,Method,DECLARES
ALMServiceImplementation,Class,getComponents,Method,DECLARES
ALMServiceImplementation,Class,getSprintWiseStories,Method,DECLARES
ALMServiceImplementation,Class,filterTrans,Method,DECLARES
ALMServiceImplementation,Class,getVelocityChart,Method,DECLARES
ALMServiceImplementation,Class,callSP,Method,DECLARES
ALMServiceImplementation,Class,calcClosedSP,Method,DECLARES
ALMServiceImplementation,Class,storyLoop,Method,DECLARES
ALMServiceImplementation,Class,storyLoopRefined,Method,DECLARES
ALMServiceImplementation,Class,updateComponentsOfTaskandSubtask,Method,DECLARES
ALMServiceImplementation,Class,getSprintProgressHome,Method,DECLARES
ALMServiceImplementation,Class,getDefectsSummaryHome,Method,DECLARES
ALMServiceImplementation,Class,getTaskRisk,Method,DECLARES
ALMServiceImplementation,Class,getActiveSprints,Method,DECLARES
ALMServiceImplementation,Class,getIssueBrakeUp,Method,DECLARES
ALMServiceImplementation,Class,getStoryProgress,Method,DECLARES
ALMServiceImplementation,Class,burndownCalculation,Method,DECLARES
ALMServiceImplementation,Class,getDefectInsightData,Method,DECLARES
ALMServiceImplementation,Class,defectParetoCalculation,Method,DECLARES
ALMServiceImplementation,Class,getProductionSlippage,Method,DECLARES
ALMServiceImplementation,Class,getDefectDensity,Method,DECLARES
ALMServiceImplementation,Class,getDefectBacklog,Method,DECLARES
ALMServiceImplementation,Class,getDefectTrendAndClassification,Method,DECLARES
ALMServiceImplementation,Class,getDefectClassification,Method,DECLARES
ALMServiceImplementation,Class,getStoryAgeingData,Method,DECLARES
ALMServiceImplementation,Class,getGroomingTable,Method,DECLARES
ALMServiceImplementation,Class,getAllIterations,Method,DECLARES
ALMServiceImplementation,Class,getAlmType,Method,DECLARES
ALMServiceImplementation,Class,saveEngScore,Method,DECLARES
ALMServiceImplementation,Class,getComponentVelocityChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentsSprintStories,Method,DECLARES
ALMServiceImplementation,Class,getIssueHierarchyChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentWiseIssueHierarchyChart,Method,DECLARES
ALMServiceImplementation,Class,getComponentsChart,Method,DECLARES
TransitionComparator,Class,compare,Method,DECLARES
SprintComparatort,Class,compare,Method,DECLARES
ALMServiceImplementation,Class,ALMServiceImplementation.Transitionrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Efforthistoryrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Changehisortyrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Authorrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Projectrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Releaserepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Iterationrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Metricrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Almconfigrepo,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Agg,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Mongotemplate,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Workingbacklog,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Workingsprints,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Velocityfields,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Closestates,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Tasknames,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Almconfig,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Vlist,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Issuelist,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Widarr,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Tempsprefined,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Tempspremoved,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Finalstoriescommited,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Storiescompleted,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Defetcscompleted,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Refinedissulist,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Removedissulist,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Logger,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Chartservice,Variable,HAS_FIELD
ALMServiceImplementation,Class,ALMServiceImplementation.Aggreg,Variable,HAS_FIELD
getAllMetrics,Method,ALMServiceImplementation.Result,Variable,USES
ALMServiceImplementation,Class,compare,Method,DECLARES
updateComponentsOfTaskandSubtask,Method,ALMServiceImplementation.Mongoaggr,Variable,USES
getSprintProgressHome,Method,ALMServiceImplementation.Result,Variable,USES
getDefectsSummaryHome,Method,ALMServiceImplementation.Projhomecalc,Variable,USES
getDefectsSummaryHome,Method,ALMServiceImplementation.Almconfig,Variable,USES
getIssueBrakeUp,Method,ALMServiceImplementation.Spcalc,Variable,USES
getGroomingTable,Method,ALMServiceImplementation.Result,Variable,USES
getAllIterations,Method,ALMServiceImplementation.Result,Variable,USES
getAllIterations,Method,ALMServiceImplementation.Iterationrepo,Variable,USES
getAlmType,Method,ALMServiceImplementation.Almtype,Variable,USES
saveEngScore,Method,ALMServiceImplementation.Proj,Variable,USES
saveEngScore,Method,ALMServiceImplementation.Engscrmap,Variable,USES
saveEngScore,Method,ALMServiceImplementation.Projectrepo,Variable,USES
getComponentVelocityChart,Method,ALMServiceImplementation.Logger,Variable,USES
getComponentVelocityChart,Method,ALMServiceImplementation.Chartservice,Variable,USES
getComponentsSprintStories,Method,ALMServiceImplementation.Logger,Variable,USES
getComponentsSprintStories,Method,ALMServiceImplementation.Chartservice,Variable,USES
getIssueHierarchyChart,Method,ALMServiceImplementation.Chartservice,Variable,USES
getComponentWiseIssueHierarchyChart,Method,ALMServiceImplementation.Logger,Variable,USES
getComponentWiseIssueHierarchyChart,Method,ALMServiceImplementation.Chartservice,Variable,USES
getComponentsChart,Method,ALMServiceImplementation.Chartservice,Variable,USES
Configurationsettingservice,Interface,getConfig,Method,DECLARES
Configurationsettingservice,Interface,addConfig,Method,DECLARES
Configurationsettingservice,Interface,deleteConfig,Method,DECLARES
Configurationsettingservice,Interface,deleteAllCollections,Method,DECLARES
Configurationsettingservice,Interface,deleteProject,Method,DECLARES
Configurationsettingservice,Interface,getConfigProject,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Configurationsettingrepository,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Almservice,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Buildrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Buildfailurepatternrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Codecoveragerepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Codequalityrep,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Healthrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Scmrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Almconfigrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Chartconfigrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Goalsettingrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Portfoliorepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Projecthealthrepo,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Userassociation,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,ConfigurationSettingServiceImplementation.Log,Variable,HAS_FIELD
ConfigurationSettingServiceImplementation,Class,getConfig,Method,DECLARES
getConfig,Method,ConfigurationSettingServiceImplementation.Lastupdated,Variable,USES
getConfig,Method,ConfigurationSettingServiceImplementation.Result,Variable,USES
ConfigurationSettingServiceImplementation,Class,addConfig,Method,DECLARES
addConfig,Method,ConfigurationSettingServiceImplementation.Date,Variable,USES
addConfig,Method,ConfigurationSettingServiceImplementation.Timestamp,Variable,USES
ConfigurationSettingServiceImplementation,Class,deleteConfig,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,deleteAllCollections,Method,DECLARES
deleteAllCollections,Method,ConfigurationSettingServiceImplementation.Failurepattern,Variable,USES
deleteAllCollections,Method,ConfigurationSettingServiceImplementation.Failurepatternobj,Variable,USES
ConfigurationSettingServiceImplementation,Class,deleteProject,Method,DECLARES
ConfigurationSettingServiceImplementation,Class,getConfigProject,Method,DECLARES
getConfigProject,Method,ConfigurationSettingServiceImplementation.Config,Variable,USES
DateUtil,Class,getDateInFormat,Method,DECLARES
getDateInFormat,Method,DateUtil.Pattern,Variable,USES
getDateInFormat,Method,DateUtil.Format,Variable,USES
getDateInFormat,Method,DateUtil.Simpledateformat,Variable,USES
DateUtil,Class,getLastWeekWorkingDateRange,Method,DECLARES
getLastWeekWorkingDateRange,Method,DateUtil.Map,Variable,USES
getLastWeekWorkingDateRange,Method,DateUtil.Date,Variable,USES
getLastWeekWorkingDateRange,Method,DateUtil.Start,Variable,USES
getLastWeekWorkingDateRange,Method,DateUtil.End,Variable,USES
Application,Class,/,Endpoint,EXPOSES
configure,Method,Sources,Method,CALLS
main,Method,configure,Method,CALLS
main,Method,Run,Method,CALLS
main,Method,getDataFromTools,Method,CALLS
TriggerCollector.Portfolioconfigrepo,Variable,TriggerCollector.List<Portfolioconfig>,Variable,FLOWS_TO
TriggerCollector.Portfolioconfig,Variable,TriggerCollector.String,Variable,FLOWS_TO
TriggerCollector.Portfolioconfig,Variable,TriggerCollector.Boolean,Variable,FLOWS_TO
TriggerCollector.String,Variable,TriggerCollector.Jobkey,Variable,TRANSFORMS_TO
TriggerCollector.Jobkey,Variable,TriggerCollector.Jobdetail,Variable,FLOWS_TO
TriggerCollector.String,Variable,TriggerCollector.Jobdetail,Variable,FLOWS_TO
TriggerCollector.String,Variable,TriggerCollector.Trigger,Variable,TRANSFORMS_TO
TriggerCollector.Portfolioconfigrepo,Variable,Portfolioconfig,Table,READS_FROM
TriggerCollector.Portfolioconfig,Variable,Portfolioconfig,Table,PERSISTS_TO
getDataFromTools,Method,Findall,Method,CALLS
getDataFromTools,Method,Checkexists,Method,CALLS
getDataFromTools,Method,Deletejob,Method,CALLS
getDataFromTools,Method,Schedulejob,Method,CALLS
getDataFromTools,Method,Start,Method,CALLS
ALMConfigController.Almconfigreq:Req,Variable,ALMConfigController.Todetailsaddsetting(Req),Variable,FLOWS_TO
AlmController.Projname,Variable,getStoryAgeingData,Method,FLOWS_TO
AlmController.Almtype,Variable,getStoryAgeingData,Method,FLOWS_TO
getStoryAgeingData,Method,AlmController.List<Componentstoryageing>,Variable,PRODUCES
AlmController.Projname,Variable,getGroomingTable,Method,FLOWS_TO
AlmController.Almtype,Variable,getGroomingTable,Method,FLOWS_TO
getGroomingTable,Method,AlmController.List<Componentgroomingtable>,Variable,PRODUCES
AlmController.Projname,Variable,delDuplicate,Method,FLOWS_TO
delDuplicate,Method,AlmController.String,Variable,PRODUCES
ALMConfigReq.Req,Variable,ALMConfigReq.Details,Variable,FLOWS_TO
ALMConfigReq.Req,Variable,ALMConfigReq.Details,Variable,TRANSFORMS_TO
toDetailsAddSetting,Method,Copyproperties,Method,CALLS
ALMToolReq.String:Almtype,Variable,ALMToolReq.String:Return,Variable,FLOWS_TO
Authentication.Username,Variable,Authentication.Feature,Variable,FLOWS_TO
Authentication.Password,Variable,Authentication.Feature,Variable,FLOWS_TO
Authentication.Feature,Variable,Authentication.Client,Variable,FLOWS_TO
Authentication.Client,Variable,Authentication.Webtarget,Variable,FLOWS_TO
Authentication.Webtarget,Variable,Authentication.Invocationbuilder,Variable,FLOWS_TO
Authentication.Invocationbuilder,Variable,Authentication.Response,Variable,FLOWS_TO
Authentication.Response,Variable,Authentication.Authenticationstatus,Variable,TRANSFORMS_TO
Authentication,Class,Http://Localhost:8081/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,EXPOSES
Authenticate,Method,Http://Localhost:8081/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,MAPS_TO
Http://Localhost:8081/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,Authentication.Username,Variable,ACCEPTS
Http://Localhost:8081/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,Authentication.Password,Variable,ACCEPTS
Http://Localhost:8081/Api/Tfsbuild?Proname=Bolt_Dashboard,Endpoint,Authentication.Response,Variable,RETURNS
Authenticationofservice,Method,Authenticate,Method,CALLS
Authenticate,Method,Register,Method,CALLS
Authenticate,Method,Target,Method,CALLS
Authenticate,Method,Request,Method,CALLS
Authenticate,Method,Get,Method,CALLS
Authenticate,Method,Printstacktrace,Method,CALLS
Authenticate,Method,Http://Localhost:8081/Api/Tfsbuild?Proname=Bolt_Dashboard,Externalservice,INVOKES
Constructor,Method,DataResponse.Result,Variable,PRODUCES
Constructor,Method,DataResponse.Lastupdated,Variable,PRODUCES
DataResponse.Result,Variable,getResult,Method,FLOWS_TO
DataResponse.Lastupdated,Variable,getLastUpdated,Method,FLOWS_TO
DataResponse,Class,Rest_Api,Endpoint,EXPOSES
Rest_Api,Endpoint,DataResponse.Result,Variable,RETURNS
Rest_Api,Endpoint,DataResponse.Lastupdated,Variable,RETURNS
ALMConfigService.Retrospective,Variable,ALMConfigService.Almconfiguration,Variable,FLOWS_TO
ALMConfigService.Projectname,Variable,ALMConfigService.String,Variable,FLOWS_TO
saveALMConfig,Method,ALMConfigService.Almconfiguration,Variable,PRODUCES
retrieveALMConfig,Method,ALMConfigService.Dataresponse<Almconfiguration>,Variable,PRODUCES
Almconfigservice,Class,/Alm-Config,Endpoint,EXPOSES
saveALMConfig,Method,/Alm-Config/Save,Endpoint,MAPS_TO
retrieveALMConfig,Method,/Alm-Config/Retrieve,Endpoint,MAPS_TO
/Alm-Config/Save,Endpoint,ALMConfigService.Almconfiguration,Variable,ACCEPTS
/Alm-Config/Retrieve,Endpoint,ALMConfigService.String,Variable,ACCEPTS
/Alm-Config/Retrieve,Endpoint,ALMConfigService.Dataresponse<Almconfiguration>,Variable,RETURNS
ALMConfigServiceImplementation.Req,Variable,ALMConfigServiceImplementation.Save(Req),Variable,FLOWS_TO
ALMConfigServiceImplementation.Get(0),Variable,ALMConfigServiceImplementation.Result,Variable,FLOWS_TO
ALMConfigServiceImplementation.Result,Variable,"ALMConfigServiceImplementation.New Dataresponse<Almconfiguration>(Result, Lastupdate)",Variable,TRANSFORMS_TO
ALMConfigServiceImplementation.Lastupdate,Variable,"ALMConfigServiceImplementation.New Dataresponse<Almconfiguration>(Result, Lastupdate)",Variable,FLOWS_TO
Save(req),Method,ALMConfigServiceImplementation.Almconfiguration,Variable,PRODUCES
Retrievealmconfig(projectname),Method,ALMConfigServiceImplementation.Dataresponse<Almconfiguration>,Variable,PRODUCES
Getprojectname()),Method,Almconfiguration,Table,READS_FROM
Findbyprojectname(projectname),Method,Almconfiguration,Table,READS_FROM
Getprojectname()),Method,Almconfiguration,Table,WRITES_TO
Save(req),Method,Almconfiguration,Table,WRITES_TO
ALMConfigServiceImplementation.Req,Variable,Almconfiguration,Table,PERSISTS_TO
ALMConfigServiceImplementation,Class,Savealmconfig,Endpoint,EXPOSES
ALMConfigServiceImplementation,Class,Retrievealmconfig,Endpoint,EXPOSES
Savealmconfig(almconfiguration),Method,Savealmconfig,Endpoint,MAPS_TO
Retrievealmconfig(string),Method,Retrievealmconfig,Endpoint,MAPS_TO
Savealmconfig,Endpoint,ALMConfigServiceImplementation.Req,Variable,ACCEPTS
Retrievealmconfig,Endpoint,ALMConfigServiceImplementation.Projectname,Variable,ACCEPTS
Savealmconfig,Endpoint,ALMConfigServiceImplementation.Almconfiguration,Variable,RETURNS
Retrievealmconfig,Endpoint,ALMConfigServiceImplementation.Dataresponse<Almconfiguration>,Variable,RETURNS
Savealmconfig(req),Method,Getprojectname()),Method,CALLS
Savealmconfig(req),Method,Save(req),Method,CALLS
Retrievealmconfig(projectname),Method,Findbyprojectname(projectname),Method,CALLS
getMetricDetails,Method,AlmService.Monogoutmetrics,Variable,PRODUCES
getAllMetrics,Method,AlmService.List<Monogoutmetrics>,Variable,PRODUCES
getChangesItems,Method,AlmService.List<Changehistorymodel>,Variable,PRODUCES
getTransitionsData,Method,AlmService.List<Transitionmodel>,Variable,PRODUCES
getIterationData,Method,AlmService.Iterationoutmodel,Variable,PRODUCES
getEffortData,Method,AlmService.List<Efforthistorymodel>,Variable,PRODUCES
getProjectDetails,Method,AlmService.List<Iterationoutmodel>,Variable,PRODUCES
getDefectCounts,Method,AlmService.Projectmodel,Variable,PRODUCES
getCrtItr,Method,AlmService.List<Iterationoutmodel>,Variable,PRODUCES
getRelease,Method,AlmService.Releasedetails,Variable,PRODUCES
getUnReleaseData,Method,AlmService.List<Metricsmodel>,Variable,PRODUCES
getDefects,Method,AlmService.List<Metricsmodel>,Variable,PRODUCES
getSlaData,Method,AlmService.List<Iterationoutmodel>,Variable,PRODUCES
getAssigneeIssues,Method,AlmService.List<Iterationoutmodel>,Variable,PRODUCES
getDateIterations,Method,AlmService.List<Iterationoutmodel>,Variable,PRODUCES
getProdDefects,Method,AlmService.List<Metricsmodel>,Variable,PRODUCES
delDuplicate,Method,AlmService.String,Variable,PRODUCES
getCurrentProjectDetails,Method,"AlmService.Map<Integer, List<Iterationoutmodel>>",Variable,PRODUCES
delAllIssues,Method,AlmService.String,Variable,PRODUCES
getAllTransitions,Method,AlmService.List<Transitionmodel>,Variable,PRODUCES
getComponentVelocity,Method,AlmService.List<Componentvelocitylist>,Variable,PRODUCES
getComponentsSprint,Method,AlmService.List<Componentsprintwisestories>,Variable,PRODUCES
getIssueHierarchy,Method,"AlmService.Map<String, List>",Variable,PRODUCES
getComponentWiseIssueHierarchy,Method,"AlmService.Map<String, Map>",Variable,PRODUCES
getComponents,Method,AlmService.List<String>,Variable,PRODUCES
getFeatureMetrics,Method,AlmService.List<Monogoutmetrics>,Variable,PRODUCES
getSprintProgressHome,Method,"AlmService.List<Map<String, List<String>>>",Variable,PRODUCES
getDefectsSummaryHome,Method,"AlmService.List<Map<String, String>>",Variable,PRODUCES
getTaskRisk,Method,AlmService.List<Componenttaskrisk>,Variable,PRODUCES
getActiveSprints,Method,AlmService.List<String>,Variable,PRODUCES
getIssueBrakeUp,Method,AlmService.List<Componentissuebreakup>,Variable,PRODUCES
getStoryProgress,Method,AlmService.List<Componentstoryprogress>,Variable,PRODUCES
burndownCalculation,Method,AlmService.List<Componentburndown>,Variable,PRODUCES
getDefectInsightData,Method,"AlmService.Map<String, List<Defectinsightdata>>",Variable,PRODUCES
defectParetoCalculation,Method,"AlmService.Map<String, List<Defectparetomodel>>",Variable,PRODUCES
getProductionSlippage,Method,"AlmService.Map<String, List<Defectproductionslippage>>",Variable,PRODUCES
getDefectDensity,Method,"AlmService.Map<String, List<Defectdensity>>",Variable,PRODUCES
getDefectBacklog,Method,"AlmService.Map<String, Defectbacklog>",Variable,PRODUCES
getDefectTrendAndClassification,Method,AlmService.Map,Variable,PRODUCES
getStoryAgeingData,Method,AlmService.List<Componentstoryageing>,Variable,PRODUCES
getGroomingTable,Method,AlmService.List<Componentgroomingtable>,Variable,PRODUCES
getAllIterations,Method,AlmService.List<Iterationmodel>,Variable,PRODUCES
getDefectClassification,Method,AlmService.Map,Variable,PRODUCES
saveEngScore,Method,AlmService.String,Variable,PRODUCES
getComponentVelocityChart,Method,AlmService.List<Componentvelocitylist>,Variable,PRODUCES
getComponentsSprintStories,Method,AlmService.List<Componentsprintwisestories>,Variable,PRODUCES
getIssueHierarchyChart,Method,"AlmService.Map<String, List>",Variable,PRODUCES
getComponentWiseIssueHierarchyChart,Method,"AlmService.Map<String, Map>",Variable,PRODUCES
getComponentsChart,Method,AlmService.List<String>,Variable,PRODUCES
Almservice,Class,Getmetricdetails,Endpoint,EXPOSES
Almservice,Class,Getallmetrics,Endpoint,EXPOSES
Almservice,Class,Getchangesitems,Endpoint,EXPOSES
Almservice,Class,Gettransitionsdata,Endpoint,EXPOSES
Almservice,Class,Getiterationdata,Endpoint,EXPOSES
Almservice,Class,Geteffortdata,Endpoint,EXPOSES
Almservice,Class,Getprojectdetails,Endpoint,EXPOSES
Almservice,Class,Getdefectcounts,Endpoint,EXPOSES
Almservice,Class,Getcrtitr,Endpoint,EXPOSES
Almservice,Class,Getrelease,Endpoint,EXPOSES
Almservice,Class,Getunreleasedata,Endpoint,EXPOSES
Almservice,Class,Getdefects,Endpoint,EXPOSES
Almservice,Class,Getsladata,Endpoint,EXPOSES
Almservice,Class,Getassigneeissues,Endpoint,EXPOSES
Almservice,Class,Getdateiterations,Endpoint,EXPOSES
Almservice,Class,Getproddefects,Endpoint,EXPOSES
Almservice,Class,Delduplicate,Endpoint,EXPOSES
Almservice,Class,Getcurrentprojectdetails,Endpoint,EXPOSES
Almservice,Class,Delallissues,Endpoint,EXPOSES
Almservice,Class,Getalltransitions,Endpoint,EXPOSES
Almservice,Class,Getcomponentvelocity,Endpoint,EXPOSES
Almservice,Class,Getcomponentssprint,Endpoint,EXPOSES
Almservice,Class,Getissuehierarchy,Endpoint,EXPOSES
Almservice,Class,Getcomponentwiseissuehierarchy,Endpoint,EXPOSES
Almservice,Class,Getcomponents,Endpoint,EXPOSES
Almservice,Class,Getfeaturemetrics,Endpoint,EXPOSES
Almservice,Class,Getsprintprogresshome,Endpoint,EXPOSES
Almservice,Class,Getdefectssummaryhome,Endpoint,EXPOSES
Almservice,Class,Gettaskrisk,Endpoint,EXPOSES
Almservice,Class,Getactivesprints,Endpoint,EXPOSES
Almservice,Class,Getissuebrakeup,Endpoint,EXPOSES
Almservice,Class,Getstoryprogress,Endpoint,EXPOSES
Almservice,Class,Burndowncalculation,Endpoint,EXPOSES
Almservice,Class,Getdefectinsightdata,Endpoint,EXPOSES
Almservice,Class,Defectparetocalculation,Endpoint,EXPOSES
Almservice,Class,Getproductionslippage,Endpoint,EXPOSES
Almservice,Class,Getdefectdensity,Endpoint,EXPOSES
Almservice,Class,Getdefectbacklog,Endpoint,EXPOSES
Almservice,Class,Getdefecttrendandclassification,Endpoint,EXPOSES
Almservice,Class,Getstoryageingdata,Endpoint,EXPOSES
Almservice,Class,Getgroomingtable,Endpoint,EXPOSES
Almservice,Class,Getalliterations,Endpoint,EXPOSES
Almservice,Class,Getdefectclassification,Endpoint,EXPOSES
Almservice,Class,Saveengscore,Endpoint,EXPOSES
Almservice,Class,Getcomponentvelocitychart,Endpoint,EXPOSES
Almservice,Class,Getcomponentssprintstories,Endpoint,EXPOSES
Almservice,Class,Getissuehierarchychart,Endpoint,EXPOSES
Almservice,Class,Getcomponentwiseissuehierarchychart,Endpoint,EXPOSES
Almservice,Class,Getcomponentschart,Endpoint,EXPOSES
ConfigurationSettingService.Req,Variable,ConfigurationSettingService.Configurationsetting,Variable,FLOWS_TO
ConfigurationSettingService.Pname,Variable,ConfigurationSettingService.String,Variable,FLOWS_TO
ConfigurationSettingService.Projectname,Variable,ConfigurationSettingService.String,Variable,FLOWS_TO
getConfig,Method,ConfigurationSettingService.Dataresponse<Iterable<Configurationsetting>>,Variable,PRODUCES
getConfigProject,Method,ConfigurationSettingService.Configurationsetting,Variable,PRODUCES
Configurationsettingservice,Class,Getconfig,Endpoint,EXPOSES
Configurationsettingservice,Class,Addconfig,Endpoint,EXPOSES
Configurationsettingservice,Class,Deleteconfig,Endpoint,EXPOSES
Configurationsettingservice,Class,Deleteallcollections,Endpoint,EXPOSES
Configurationsettingservice,Class,Deleteproject,Endpoint,EXPOSES
Configurationsettingservice,Class,Getconfigproject,Endpoint,EXPOSES
getConfig,Method,Getconfig,Endpoint,MAPS_TO
addConfig,Method,Addconfig,Endpoint,MAPS_TO
deleteConfig,Method,Deleteconfig,Endpoint,MAPS_TO
deleteAllCollections,Method,Deleteallcollections,Endpoint,MAPS_TO
deleteProject,Method,Deleteproject,Endpoint,MAPS_TO
getConfigProject,Method,Getconfigproject,Endpoint,MAPS_TO
Getconfig,Endpoint,ConfigurationSettingService.Dataresponse<Iterable<Configurationsetting>>,Variable,RETURNS
Addconfig,Endpoint,ConfigurationSettingService.Configurationsetting,Variable,ACCEPTS
Addconfig,Endpoint,ConfigurationSettingService.Configurationsetting,Variable,RETURNS
Deleteconfig,Endpoint,ConfigurationSettingService.Configurationsetting,Variable,ACCEPTS
Deleteconfig,Endpoint,ConfigurationSettingService.Int,Variable,RETURNS
Deleteallcollections,Endpoint,ConfigurationSettingService.String,Variable,ACCEPTS
Deleteallcollections,Endpoint,ConfigurationSettingService.Boolean,Variable,RETURNS
Deleteproject,Endpoint,ConfigurationSettingService.String,Variable,ACCEPTS
Deleteproject,Endpoint,ConfigurationSettingService.Boolean,Variable,RETURNS
Getconfigproject,Endpoint,ConfigurationSettingService.String,Variable,ACCEPTS
Getconfigproject,Endpoint,ConfigurationSettingService.Configurationsetting,Variable,RETURNS
ConfigurationSettingServiceImplementation.Date,Variable,ConfigurationSettingServiceImplementation.Timestamp,Variable,FLOWS_TO
ConfigurationSettingServiceImplementation.Date,Variable,ConfigurationSettingServiceImplementation.Timestamp,Variable,TRANSFORMS_TO
ConfigurationSettingServiceImplementation.Req,Variable,ConfigurationSettingServiceImplementation.Timestamp,Variable,FLOWS_TO
ConfigurationSettingServiceImplementation.Failurepattern,Variable,ConfigurationSettingServiceImplementation.Failurepatternobj,Variable,FLOWS_TO
Configurationsettingrepository,Method,Configurationsetting,Table,READS_FROM
ConfigurationSettingServiceImplementation.Req,Variable,Configurationsetting,Table,PERSISTS_TO
Configurationsettingrepository,Method,Configurationsetting,Table,WRITES_TO
Buildrepo,Method,Buildtool,Table,READS_FROM
Buildrepo,Method,Buildtool,Table,WRITES_TO
Buildfailurepatternrepo,Method,Buildfailurepatternforproject,Table,READS_FROM
Buildfailurepatternrepo,Method,Buildfailurepatternforproject,Table,WRITES_TO
Codecoveragerepo,Method,Codecoverage,Table,READS_FROM
Codecoveragerepo,Method,Codecoverage,Table,WRITES_TO
Codequalityrep,Method,Codequality,Table,READS_FROM
Codequalityrep,Method,Codequality,Table,WRITES_TO
Healthrepo,Method,Healthdata,Table,READS_FROM
Healthrepo,Method,Healthdata,Table,WRITES_TO
Scmrepo,Method,Scmtool,Table,READS_FROM
Scmrepo,Method,Scmtool,Table,WRITES_TO
Almconfigrepo,Method,Almconfig,Table,READS_FROM
Almconfigrepo,Method,Almconfig,Table,WRITES_TO
Goalsettingrepo,Method,Goalsetting,Table,READS_FROM
Goalsettingrepo,Method,Goalsetting,Table,WRITES_TO
Portfoliorepo,Method,Portfolioconfig,Table,READS_FROM
Portfoliorepo,Method,Portfolioconfig,Table,WRITES_TO
Projecthealthrepo,Method,Projecthealth,Table,READS_FROM
Projecthealthrepo,Method,Projecthealth,Table,WRITES_TO
Chartconfigrepo,Method,Chartconfig,Table,READS_FROM
Chartconfigrepo,Method,Chartconfig,Table,WRITES_TO
getConfig,Method,/Getconfig,Endpoint,MAPS_TO
addConfig,Method,/Addconfig,Endpoint,MAPS_TO
deleteConfig,Method,/Deleteconfig,Endpoint,MAPS_TO
deleteAllCollections,Method,/Deleteallcollections,Endpoint,MAPS_TO
deleteProject,Method,/Deleteproject,Endpoint,MAPS_TO
getConfigProject,Method,/Getconfigproject,Endpoint,MAPS_TO
/Addconfig,Endpoint,ConfigurationSettingServiceImplementation.Req,Variable,ACCEPTS
/Deleteconfig,Endpoint,ConfigurationSettingServiceImplementation.Configurationsetting,Variable,ACCEPTS
/Deleteallcollections,Endpoint,ConfigurationSettingServiceImplementation.Projectname,Variable,ACCEPTS
/Deleteproject,Endpoint,ConfigurationSettingServiceImplementation.Projectname,Variable,ACCEPTS
/Getconfigproject,Endpoint,ConfigurationSettingServiceImplementation.Pname,Variable,ACCEPTS
/Getconfig,Endpoint,ConfigurationSettingServiceImplementation.Dataresponse<Iterable<Configurationsetting>>,Variable,RETURNS
/Addconfig,Endpoint,ConfigurationSettingServiceImplementation.Configurationsetting,Variable,RETURNS
/Deleteconfig,Endpoint,ConfigurationSettingServiceImplementation.Int,Variable,RETURNS
/Deleteallcollections,Endpoint,ConfigurationSettingServiceImplementation.Boolean,Variable,RETURNS
/Deleteproject,Endpoint,ConfigurationSettingServiceImplementation.Boolean,Variable,RETURNS
/Getconfigproject,Endpoint,ConfigurationSettingServiceImplementation.Configurationsetting,Variable,RETURNS
deleteProject,Method,deleteAllCollections,Method,CALLS
deleteAllCollections,Method,Externalservice:Almservice.Delallissues(),Externalservice,INVOKES
getConfigProject,Method,Externalservice:Encryptiondecryptionaes.Decrypt(),Externalservice,INVOKES
DateUtil.Format,Variable,DateUtil.Pattern,Variable,FLOWS_TO
DateUtil.Dateinput,Variable,DateUtil.Simpledateformat,Variable,FLOWS_TO
DateUtil.Simpledateformat,Variable,DateUtil.Return,Variable,TRANSFORMS_TO
getDateInFormat,Method,"DateUtil.Get(""Start"")",Variable,PRODUCES
getDateInFormat,Method,"DateUtil.Get(""End"")",Variable,PRODUCES
DateUtil.Start,Variable,"DateUtil.Get(""Start"")",Variable,TRANSFORMS_TO
DateUtil.End,Variable,"DateUtil.Get(""End"")",Variable,TRANSFORMS_TO
DateUtil,Class,/Getdateinformat,Endpoint,EXPOSES
DateUtil,Class,/Getlastweekworkingdaterange,Endpoint,EXPOSES
getDateInFormat,Method,/Getdateinformat,Endpoint,MAPS_TO
getLastWeekWorkingDateRange,Method,/Getlastweekworkingdaterange,Endpoint,MAPS_TO
/Getdateinformat,Endpoint,DateUtil.Format,Variable,ACCEPTS
/Getdateinformat,Endpoint,DateUtil.Dateinput,Variable,ACCEPTS
/Getdateinformat,Endpoint,DateUtil.Return,Variable,RETURNS
/Getlastweekworkingdaterange,Endpoint,DateUtil.Map,Variable,RETURNS
getLastWeekWorkingDateRange,Method,getDateInFormat,Method,CALLS
getDateInFormat,Method,Format,Method,CALLS
