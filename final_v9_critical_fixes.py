# Critical Fixes for final_v9.ipynb Pipeline
# These functions should replace the corresponding functions in the notebook

import time
import uuid
from tenacity import retry, stop_after_attempt, wait_exponential
import pandas as pd

# ========== FIX 1: Enhanced Stage 2B with Interface Support ==========

def extract_file_class_relationships_ast_fixed():
    """Extract file-class relationships using AST parsing - FIXED VERSION"""
    file_class_relationships = []
    
    # Get all Java files from the hierarchy data
    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']
    
    for _, file_row in java_files.iterrows():
        if 'file_path' in file_row and file_row['file_path']:
            file_path = file_row['file_path']
            file_name = file_row['destination_node']
            
            try:
                # Read and parse the Java file
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code = f.read().encode('utf-8')
                
                tree = parser.parse(source_code)
                root_node = tree.root_node
                
                # Find class and interface declarations - FIXED
                def find_classes_and_interfaces(node):
                    entities = []
                    if node.type == 'class_declaration':
                        # Find class name
                        for child in node.children:
                            if child.type == 'identifier':
                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(class_name), 'Class'))
                                break
                    elif node.type == 'interface_declaration':
                        # Find interface name - NEW
                        for child in node.children:
                            if child.type == 'identifier':
                                interface_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(interface_name), 'Interface'))
                                break
                    elif node.type == 'enum_declaration':
                        # Find enum name - BONUS
                        for child in node.children:
                            if child.type == 'identifier':
                                enum_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(enum_name), 'Enum'))
                                break
                    
                    # Recursively search child nodes
                    for child in node.children:
                        entities.extend(find_classes_and_interfaces(child))
                    
                    return entities
                
                entities_in_file = find_classes_and_interfaces(root_node)
                
                # Create file-class/interface relationships - FIXED
                for entity_name, entity_type in entities_in_file:
                    file_class_relationships.append({
                        'source_node': file_name,
                        'source_type': 'File',
                        'destination_node': entity_name,
                        'destination_type': entity_type,
                        'relationship': 'DECLARES',
                        'file_path': file_path
                    })
                    
            except Exception as e:
                print(f"⚠️ Error processing {file_path}: {e}")
                continue
    
    return file_class_relationships

# ========== FIX 2: Rate Limiting with Retry Logic ==========

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=60))
def process_with_retry(transformer, doc):
    """Process LLM request with retry logic for rate limiting - NEW"""
    try:
        return transformer.convert_to_graph_documents([doc])
    except Exception as e:
        error_str = str(e).lower()
        if "429" in error_str or "rate limit" in error_str or "quota" in error_str:
            print(f"🔄 Rate limit hit, retrying after delay...")
            raise  # This will trigger the retry
        else:
            print(f"❌ Non-rate-limit error: {e}")
            raise  # Re-raise non-rate-limit errors

def enhanced_llm_processing_with_retry(class_name, class_info, transformer):
    """Enhanced LLM processing with retry logic - FIXED"""
    try:
        # Create document for processing
        doc = Document(
            page_content=class_info.get('source_code', ''),
            metadata={
                'class_name': class_name,
                'file_path': class_info.get('file_path', ''),
                'package': class_info.get('package', '')
            }
        )
        
        # Process with retry logic
        graph_documents = process_with_retry(transformer, doc)
        
        return graph_documents
        
    except Exception as e:
        print(f"❌ Failed to process {class_name} after retries: {e}")
        return []

# ========== FIX 3: Enhanced Variable Context Handling ==========

def create_consistent_variable_name(var_name, context_name, context_type='method'):
    """Create consistent variable name with proper context handling - NEW"""
    clean_var = extract_clean_name(var_name, 'variable')
    clean_context = extract_clean_name(context_name, context_type)
    
    # For Neo4j: store only variable name, context as separate property
    return {
        'display_name': clean_var,
        'context': clean_context,
        'context_type': context_type,
        'full_identifier': f"{clean_context}.{clean_var}",
        'unique_id': f"{context_type}_{clean_context}_{clean_var}_{uuid.uuid4().hex[:6]}"
    }

def standardize_variable_relationships(df_relationships):
    """Standardize variable relationships across the pipeline - NEW"""
    standardized_relationships = []
    
    for _, row in df_relationships.iterrows():
        if row['destination_type'] == 'Variable':
            # Parse variable information
            var_name = row['destination_node']
            source_name = row['source_node']
            relationship = row['relationship']
            
            # Determine context type
            if relationship == 'HAS_FIELD':
                context_type = 'class'
                context_name = source_name
            elif relationship == 'USES' or relationship == 'DECLARES':
                context_type = 'method'
                context_name = source_name
            else:
                context_type = 'unknown'
                context_name = source_name
            
            # Create consistent variable info
            var_info = create_consistent_variable_name(var_name, context_name, context_type)
            
            # Update relationship with standardized variable
            standardized_relationships.append({
                'source_node': row['source_node'],
                'source_type': row['source_type'],
                'destination_node': var_info['display_name'],
                'destination_type': 'Variable',
                'relationship': row['relationship'],
                'file_path': row.get('file_path', ''),
                'variable_context': var_info['context'],
                'variable_context_type': var_info['context_type'],
                'variable_full_id': var_info['full_identifier'],
                'variable_unique_id': var_info['unique_id']
            })
        else:
            # Keep non-variable relationships as-is
            standardized_relationships.append(row.to_dict())
    
    return pd.DataFrame(standardized_relationships)

# ========== FIX 4: Enhanced AST Name Mapping ==========

def create_enhanced_ast_name_mapping_fixed(df_ast, class_registry):
    """Create enhanced AST name mapping with case-insensitive matching - FIXED"""
    print("🔧 Creating enhanced AST name mapping with case-insensitive matching...")
    
    enhanced_mapping = {}
    
    # Get all unique names from AST
    ast_names = set()
    for col in ['source_node', 'destination_node']:
        if col in df_ast.columns:
            ast_names.update(df_ast[col].dropna().unique())
    
    # Get all class registry names
    registry_names = set(class_registry.keys())
    
    print(f"📊 AST names: {len(ast_names)}, Registry names: {len(registry_names)}")
    
    # Create case-insensitive mapping
    for ast_name in ast_names:
        if not ast_name or pd.isna(ast_name):
            continue
            
        # First try exact match
        if ast_name in registry_names:
            enhanced_mapping[ast_name.lower()] = ast_name
            continue
        
        # Try case-insensitive match with class registry
        for registry_name in registry_names:
            if ast_name.lower() == registry_name.lower():
                enhanced_mapping[ast_name.lower()] = registry_name
                print(f"  🔗 Case-insensitive match: {ast_name} -> {registry_name}")
                break
        
        # Try partial matching for method names (e.g., "getUserId" -> "GetUserId")
        if ast_name.lower() not in enhanced_mapping:
            for registry_name in registry_names:
                if (ast_name.lower().replace('_', '').replace('-', '') == 
                    registry_name.lower().replace('_', '').replace('-', '')):
                    enhanced_mapping[ast_name.lower()] = registry_name
                    print(f"  🔗 Partial match: {ast_name} -> {registry_name}")
                    break
    
    print(f"✅ Enhanced mapping created: {len(enhanced_mapping)} mappings")
    return enhanced_mapping

# ========== FIX 5: Enhanced Duplicate Removal ==========

def remove_duplicates_enhanced(df_final):
    """Remove duplicates with enhanced logic - NEW"""
    print("🔧 Removing duplicates with enhanced logic...")
    
    initial_count = len(df_final)
    
    # First pass: remove exact duplicates
    df_final = df_final.drop_duplicates(
        subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'],
        keep='first'
    )
    
    after_exact = len(df_final)
    
    # Second pass: remove semantic duplicates (same relationship, different context)
    # Keep the one with more specific context (e.g., with file_path)
    df_final = df_final.sort_values(['file_path', 'variable_context'], na_last=False)
    df_final = df_final.drop_duplicates(
        subset=['source_node', 'destination_node', 'relationship'],
        keep='first'
    )
    
    final_count = len(df_final)
    
    print(f"✅ Duplicate removal complete:")
    print(f"   📊 Initial: {initial_count}")
    print(f"   📊 After exact removal: {after_exact}")
    print(f"   📊 Final: {final_count}")
    print(f"   📊 Removed: {initial_count - final_count} duplicates")
    
    return df_final

# ========== FIX 6: Memory-Optimized Processing ==========

def process_classes_in_batches(class_registry, batch_size=10):
    """Process classes in smaller batches to prevent memory issues - NEW"""
    class_names = list(class_registry.keys())
    
    print(f"🔧 Processing {len(class_names)} classes in batches of {batch_size}")
    
    for i in range(0, len(class_names), batch_size):
        batch = class_names[i:i+batch_size]
        
        print(f"   📦 Processing batch {i//batch_size + 1}: {len(batch)} classes")
        
        # Create isolated memory context for this batch
        batch_memory = {
            'variable_contexts': {},
            'method_signatures': {},
            'current_batch_edges': set()
        }
        
        yield batch, batch_memory
        
        # Clean up after batch
        del batch_memory
        import gc
        gc.collect()

print("✅ Critical fixes loaded successfully!")
print("📝 To apply these fixes:")
print("   1. Replace the corresponding functions in final_v9.ipynb")
print("   2. Test with a small codebase first")
print("   3. Monitor memory usage and performance")
print("   4. Validate Neo4j data quality")
