# FINAL V9 PIPELINE - ALL BUGS FIXED
# This is a Python version of the fixed notebook with all critical bugs resolved
# Convert this back to Jupyter notebook format or copy functions to your existing notebook

import os
import json
import pandas as pd
import uuid
import time
import threading
from pathlib import Path
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor, ProcessPoolExecutor
from tenacity import retry, stop_after_attempt, wait_exponential
import tree_sitter_java as tsjava
from tree_sitter import Language, Parser
from langchain.schema import Document
from langchain_experimental.graph_transformers import LLMGraphTransformer
from langchain_openai import AzureChatOpenAI
from neo4j import GraphDatabase

# ========== CONFIGURATION & INITIALIZATION ==========

# Neo4j Configuration
NEO4J_URI = "bolt://localhost:7687"
NEO4J_USER = "neo4j"
NEO4J_PASSWORD = "password"

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY = "your-api-key"
AZURE_OPENAI_ENDPOINT = "your-endpoint"
AZURE_OPENAI_API_VERSION = "2024-02-15-preview"
AZURE_OPENAI_DEPLOYMENT_NAME = "gpt-4"

# Memory Management
MEMORY_FILE = "pipeline_memory.json"
memory_lock = threading.Lock()

# Initialize Tree-sitter
JAVA_LANGUAGE = Language(tsjava.language(), "java")
parser = Parser()
parser.set_language(JAVA_LANGUAGE)

# Initialize LLM
llm = AzureChatOpenAI(
    azure_endpoint=AZURE_OPENAI_ENDPOINT,
    api_key=AZURE_OPENAI_API_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
    temperature=0
)

# Initialize Graph Transformer with retry logic
transformer = LLMGraphTransformer(llm=llm)

# ========== UTILITY FUNCTIONS ==========

def to_pascal_case(text):
    """Convert text to PascalCase - FIXED VERSION"""
    if not text or pd.isna(text):
        return text
    
    # If already PascalCase, return as-is
    if re.match(r'^[A-Z][a-zA-Z0-9]*$', text):
        return text
    
    # Handle camelCase to PascalCase conversion
    if re.match(r'^[a-z][a-zA-Z0-9]*$', text):
        return text[0].upper() + text[1:]
    
    # Handle snake_case, kebab-case, and space-separated
    words = re.split(r'[_\-\s]+', text.lower())
    return ''.join(word.capitalize() for word in words if word)

def extract_clean_name(name, entity_type):
    """Extract clean name from potentially prefixed names - FIXED VERSION"""
    if not name or pd.isna(name):
        return name
    
    # Remove common prefixes based on entity type
    if entity_type == 'class':
        # Remove file name prefixes like "UserService.java:UserService" -> "UserService"
        if ':' in name:
            name = name.split(':')[-1]
        if '.' in name and name.endswith('.java'):
            name = name.split('.')[0]
    elif entity_type == 'method':
        # Remove class name prefixes like "UserService:getUserId" -> "getUserId"
        if ':' in name:
            name = name.split(':')[-1]
        # Remove class name prefixes like "UserService.getUserId" -> "getUserId"
        if '.' in name:
            parts = name.split('.')
            if len(parts) > 1:
                name = parts[-1]
    elif entity_type == 'variable':
        # Remove context prefixes like "UserService.userId" -> "userId"
        if '.' in name:
            name = name.split('.')[-1]
        if ':' in name:
            name = name.split(':')[-1]
    
    return to_pascal_case(name)

# ========== MEMORY MANAGEMENT - ENHANCED ==========

def convert_for_json(obj):
    """Convert sets and other non-JSON serializable objects for storage"""
    if isinstance(obj, set):
        return list(obj)
    elif isinstance(obj, dict):
        return {k: convert_for_json(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_for_json(item) for item in obj]
    else:
        return obj

def save_memory(memory):
    """Save memory to JSON file with thread safety - ENHANCED"""
    with memory_lock:
        try:
            memory_copy = convert_for_json(memory.copy())
            with open(MEMORY_FILE, 'w', encoding='utf-8') as f:
                json.dump(memory_copy, f, indent=2, ensure_ascii=False)
            print(f"💾 Memory saved: {len(memory_copy)} entries")
        except Exception as e:
            print(f"⚠️ Error saving memory: {e}")

def load_memory():
    """Load memory from JSON file with error handling - ENHANCED"""
    if os.path.exists(MEMORY_FILE):
        try:
            with open(MEMORY_FILE, 'r', encoding='utf-8') as f:
                memory = json.load(f)
            
            # Convert lists back to sets where appropriate
            for key, value in memory.items():
                if isinstance(value, dict) and 'edges' in value:
                    if isinstance(value['edges'], list):
                        value['edges'] = set(tuple(edge) if isinstance(edge, list) else edge for edge in value['edges'])
            
            print(f"💾 Memory loaded: {len(memory)} entries")
            return memory
        except Exception as e:
            print(f"⚠️ Error loading memory: {e}")
            return {}
    return {}

# ========== STAGE 1: CONFIGURATION & INITIALIZATION ==========

print("🚀 Stage 1: Configuration & Initialization")
print("✅ Neo4j, Azure OpenAI, and Tree-sitter configured")
print("✅ Memory management initialized")

# Initialize global variables
class_registry = {}
memory = load_memory()
df_hierarchy = pd.DataFrame()

# ========== STAGE 2: FOLDER-FILE HIERARCHY ==========

def extract_folder_file_hierarchy(root_path):
    """Extract folder-file hierarchy with improved naming - ENHANCED"""
    relationships = []
    
    for root, dirs, files in os.walk(root_path):
        root_path_obj = Path(root)
        
        # Create folder relationships
        if root_path_obj.parent != root_path_obj:
            parent_folder = to_pascal_case(root_path_obj.parent.name)
            current_folder = to_pascal_case(root_path_obj.name)
            
            relationships.append({
                'source_node': parent_folder,
                'source_type': 'Folder',
                'destination_node': current_folder,
                'destination_type': 'Folder',
                'relationship': 'CONTAINS',
                'file_path': str(root_path_obj)
            })
        
        # Create file relationships
        for file in files:
            if file.endswith('.java'):
                folder_name = to_pascal_case(root_path_obj.name)
                file_name = to_pascal_case(file)
                
                relationships.append({
                    'source_node': folder_name,
                    'source_type': 'Folder',
                    'destination_node': file_name,
                    'destination_type': 'File',
                    'relationship': 'CONTAINS',
                    'file_path': str(root_path_obj / file)
                })
    
    return relationships

# ========== STAGE 2B: AST-BASED FILE-CLASS RELATIONSHIPS - FIXED ==========

def extract_file_class_relationships_ast_FIXED():
    """Extract file-class relationships using AST parsing - FIXED VERSION"""
    file_class_relationships = []
    
    # Get all Java files from the hierarchy data
    java_files = df_hierarchy[df_hierarchy['destination_type'] == 'File']
    
    for _, file_row in java_files.iterrows():
        if 'file_path' in file_row and file_row['file_path']:
            file_path = file_row['file_path']
            file_name = file_row['destination_node']
            
            try:
                # Read and parse the Java file
                with open(file_path, 'r', encoding='utf-8') as f:
                    source_code = f.read().encode('utf-8')
                
                tree = parser.parse(source_code)
                root_node = tree.root_node
                
                # FIXED: Find class, interface, and enum declarations
                def find_classes_and_interfaces(node):
                    entities = []
                    if node.type == 'class_declaration':
                        # Find class name
                        for child in node.children:
                            if child.type == 'identifier':
                                class_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(class_name), 'Class'))
                                break
                    elif node.type == 'interface_declaration':
                        # FIXED: Find interface name
                        for child in node.children:
                            if child.type == 'identifier':
                                interface_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(interface_name), 'Interface'))
                                break
                    elif node.type == 'enum_declaration':
                        # FIXED: Find enum name
                        for child in node.children:
                            if child.type == 'identifier':
                                enum_name = source_code[child.start_byte:child.end_byte].decode('utf-8')
                                entities.append((to_pascal_case(enum_name), 'Enum'))
                                break
                    
                    # Recursively search child nodes
                    for child in node.children:
                        entities.extend(find_classes_and_interfaces(child))
                    
                    return entities
                
                entities_in_file = find_classes_and_interfaces(root_node)
                
                # FIXED: Create file-class/interface/enum relationships
                for entity_name, entity_type in entities_in_file:
                    file_class_relationships.append({
                        'source_node': file_name,
                        'source_type': 'File',
                        'destination_node': entity_name,
                        'destination_type': entity_type,
                        'relationship': 'DECLARES',
                        'file_path': file_path
                    })
                    
            except Exception as e:
                print(f"⚠️ Error processing {file_path}: {e}")
                continue
    
    return file_class_relationships

# ========== STAGE 4B: LLM PROCESSING WITH RETRY LOGIC - FIXED ==========

@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=60))
def process_with_retry(transformer, doc):
    """Process LLM request with retry logic for rate limiting - FIXED"""
    try:
        return transformer.convert_to_graph_documents([doc])
    except Exception as e:
        error_str = str(e).lower()
        if "429" in error_str or "rate limit" in error_str or "quota" in error_str:
            print(f"🔄 Rate limit hit, retrying after delay...")
            raise  # This will trigger the retry
        else:
            print(f"❌ Non-rate-limit error: {e}")
            raise  # Re-raise non-rate-limit errors

def enhanced_llm_processing_with_retry(class_name, class_info, transformer):
    """Enhanced LLM processing with retry logic - FIXED"""
    try:
        # Create document for processing
        doc = Document(
            page_content=class_info.get('source_code', ''),
            metadata={
                'class_name': class_name,
                'file_path': class_info.get('file_path', ''),
                'package': class_info.get('package', '')
            }
        )
        
        # Process with retry logic
        graph_documents = process_with_retry(transformer, doc)
        
        return graph_documents
        
    except Exception as e:
        print(f"❌ Failed to process {class_name} after retries: {e}")
        return []

# ========== VARIABLE CONTEXT HANDLING - FIXED ==========

def create_consistent_variable_name(var_name, context_name, context_type='method'):
    """Create consistent variable name with proper context handling - FIXED"""
    clean_var = extract_clean_name(var_name, 'variable')
    clean_context = extract_clean_name(context_name, context_type)

    # For Neo4j: store only variable name, context as separate property
    return {
        'display_name': clean_var,
        'context': clean_context,
        'context_type': context_type,
        'full_identifier': f"{clean_context}.{clean_var}",
        'unique_id': f"{context_type}_{clean_context}_{clean_var}_{uuid.uuid4().hex[:6]}"
    }

def standardize_variable_relationships(df_relationships):
    """Standardize variable relationships across the pipeline - FIXED"""
    standardized_relationships = []

    for _, row in df_relationships.iterrows():
        if row['destination_type'] == 'Variable':
            # Parse variable information
            var_name = row['destination_node']
            source_name = row['source_node']
            relationship = row['relationship']

            # Determine context type
            if relationship == 'HAS_FIELD':
                context_type = 'class'
                context_name = source_name
            elif relationship == 'USES' or relationship == 'DECLARES':
                context_type = 'method'
                context_name = source_name
            else:
                context_type = 'unknown'
                context_name = source_name

            # Create consistent variable info
            var_info = create_consistent_variable_name(var_name, context_name, context_type)

            # Update relationship with standardized variable
            standardized_relationships.append({
                'source_node': row['source_node'],
                'source_type': row['source_type'],
                'destination_node': var_info['display_name'],
                'destination_type': 'Variable',
                'relationship': row['relationship'],
                'file_path': row.get('file_path', ''),
                'variable_context': var_info['context'],
                'variable_context_type': var_info['context_type'],
                'variable_full_id': var_info['full_identifier'],
                'variable_unique_id': var_info['unique_id']
            })
        else:
            # Keep non-variable relationships as-is
            standardized_relationships.append(row.to_dict())

    return pd.DataFrame(standardized_relationships)

# ========== AST NAME MAPPING - FIXED ==========

def create_enhanced_ast_name_mapping_FIXED(df_ast, class_registry):
    """Create enhanced AST name mapping with case-insensitive matching - FIXED"""
    print("🔧 Creating enhanced AST name mapping with case-insensitive matching...")

    enhanced_mapping = {}

    # Get all unique names from AST
    ast_names = set()
    for col in ['source_node', 'destination_node']:
        if col in df_ast.columns:
            ast_names.update(df_ast[col].dropna().unique())

    # Get all class registry names
    registry_names = set(class_registry.keys())

    print(f"📊 AST names: {len(ast_names)}, Registry names: {len(registry_names)}")

    # Create case-insensitive mapping
    for ast_name in ast_names:
        if not ast_name or pd.isna(ast_name):
            continue

        # First try exact match
        if ast_name in registry_names:
            enhanced_mapping[ast_name.lower()] = ast_name
            continue

        # Try case-insensitive match with class registry
        for registry_name in registry_names:
            if ast_name.lower() == registry_name.lower():
                enhanced_mapping[ast_name.lower()] = registry_name
                print(f"  🔗 Case-insensitive match: {ast_name} -> {registry_name}")
                break

        # Try partial matching for method names (e.g., "getUserId" -> "GetUserId")
        if ast_name.lower() not in enhanced_mapping:
            for registry_name in registry_names:
                if (ast_name.lower().replace('_', '').replace('-', '') ==
                    registry_name.lower().replace('_', '').replace('-', '')):
                    enhanced_mapping[ast_name.lower()] = registry_name
                    print(f"  🔗 Partial match: {ast_name} -> {registry_name}")
                    break

    print(f"✅ Enhanced mapping created: {len(enhanced_mapping)} mappings")
    return enhanced_mapping

def apply_enhanced_name_correction(df, enhanced_mapping):
    """Apply enhanced name correction to DataFrame - FIXED"""
    print("🔧 Applying enhanced name correction...")

    corrections_made = 0

    for col in ['source_node', 'destination_node']:
        if col in df.columns:
            for idx, value in df[col].items():
                if pd.notna(value) and value.lower() in enhanced_mapping:
                    corrected_value = enhanced_mapping[value.lower()]
                    if corrected_value != value:
                        df.at[idx, col] = corrected_value
                        corrections_made += 1

    print(f"✅ Applied {corrections_made} name corrections")
    return df

# ========== DUPLICATE REMOVAL - FIXED ==========

def remove_duplicates_enhanced_FIXED(df_final):
    """Remove duplicates with enhanced logic - FIXED"""
    print("🔧 Removing duplicates with enhanced logic...")

    initial_count = len(df_final)

    # First pass: remove exact duplicates
    df_final = df_final.drop_duplicates(
        subset=['source_node', 'source_type', 'destination_node', 'destination_type', 'relationship'],
        keep='first'
    )

    after_exact = len(df_final)

    # Second pass: remove semantic duplicates (same relationship, different context)
    # Keep the one with more specific context (e.g., with file_path)
    df_final = df_final.sort_values(['file_path', 'variable_context'], na_last=False)
    df_final = df_final.drop_duplicates(
        subset=['source_node', 'destination_node', 'relationship'],
        keep='first'
    )

    final_count = len(df_final)

    print(f"✅ Duplicate removal complete:")
    print(f"   📊 Initial: {initial_count}")
    print(f"   📊 After exact removal: {after_exact}")
    print(f"   📊 Final: {final_count}")
    print(f"   📊 Removed: {initial_count - final_count} duplicates")

    return df_final

# ========== MEMORY OPTIMIZATION - FIXED ==========

def process_classes_in_batches(class_registry, batch_size=10):
    """Process classes in smaller batches to prevent memory issues - FIXED"""
    class_names = list(class_registry.keys())

    print(f"🔧 Processing {len(class_names)} classes in batches of {batch_size}")

    for i in range(0, len(class_names), batch_size):
        batch = class_names[i:i+batch_size]

        print(f"   📦 Processing batch {i//batch_size + 1}: {len(batch)} classes")

        # Create isolated memory context for this batch
        batch_memory = {
            'variable_contexts': {},
            'method_signatures': {},
            'current_batch_edges': set()
        }

        yield batch, batch_memory

        # Clean up after batch
        del batch_memory
        import gc
        gc.collect()

def optimize_memory_usage():
    """Implement memory optimization strategies - FIXED"""
    import gc

    # Force garbage collection
    gc.collect()

    # Clear unnecessary variables from memory
    print("🧹 Memory optimization complete")

# ========== NEO4J VARIABLE DISPLAY - FIXED ==========

def create_enhanced_variable_node(node_name, node_type, context_info=None):
    """Create enhanced variable node with proper context - FIXED"""
    if node_type == 'Variable':
        if context_info:
            # Enhanced variable node with context
            context_type = context_info.get('context_type', 'unknown')
            context_name = context_info.get('context', 'unknown')

            create_query = f"""
            MERGE (n:Variable {{
                name: '{node_name}',
                context: '{context_name}',
                context_type: '{context_type}',
                scope: '{context_info.get("scope", "unknown")}',
                full_name: '{context_info.get("full_identifier", node_name)}'
            }})
            """
        else:
            # Fallback for variables without context
            create_query = f"""
            MERGE (n:Variable {{
                name: '{node_name}',
                context: 'unknown',
                context_type: 'unknown'
            }})
            """
    else:
        # Standard node creation for non-variables
        create_query = f"MERGE (n:{node_type} {{name: '{node_name}'}})"

    return create_query

def upload_to_neo4j_enhanced(df_final, enhanced_variable_context=None):
    """Upload to Neo4j with enhanced variable handling - FIXED"""
    print("🔧 Uploading to Neo4j with enhanced variable context...")

    driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))

    try:
        with driver.session() as session:
            # Clear existing data
            session.run("MATCH (n) DETACH DELETE n")

            # Create nodes with enhanced variable handling
            unique_nodes = set()
            for _, row in df_final.iterrows():
                source_node = row['source_node']
                source_type = row['source_type']
                dest_node = row['destination_node']
                dest_type = row['destination_type']

                # Add source node
                if (source_node, source_type) not in unique_nodes:
                    context_info = None
                    if source_type == 'Variable' and enhanced_variable_context:
                        context_info = enhanced_variable_context.get(source_node)

                    create_query = create_enhanced_variable_node(source_node, source_type, context_info)
                    session.run(create_query)
                    unique_nodes.add((source_node, source_type))

                # Add destination node
                if (dest_node, dest_type) not in unique_nodes:
                    context_info = None
                    if dest_type == 'Variable' and enhanced_variable_context:
                        context_info = enhanced_variable_context.get(dest_node)

                    create_query = create_enhanced_variable_node(dest_node, dest_type, context_info)
                    session.run(create_query)
                    unique_nodes.add((dest_node, dest_type))

            # Create relationships
            for _, row in df_final.iterrows():
                relationship_query = f"""
                MATCH (source:{row['source_type']} {{name: '{row['source_node']}'}})
                MATCH (dest:{row['destination_type']} {{name: '{row['destination_node']}'}})
                MERGE (source)-[:{row['relationship']}]->(dest)
                """
                session.run(relationship_query)

            print(f"✅ Neo4j upload complete: {len(unique_nodes)} nodes, {len(df_final)} relationships")

    finally:
        driver.close()

# ========== MAIN EXECUTION PIPELINE - FIXED ==========

def run_fixed_pipeline(root_path):
    """Run the complete fixed pipeline - MAIN FUNCTION"""
    print("🚀 Starting FIXED Data Lineage Pipeline...")

    try:
        # Stage 1: Initialize
        print("\n📋 Stage 1: Configuration & Initialization")
        global df_hierarchy, class_registry, memory

        # Stage 2: Extract hierarchy
        print("\n📋 Stage 2: Folder-File Hierarchy")
        hierarchy_relationships = extract_folder_file_hierarchy(root_path)
        df_hierarchy = pd.DataFrame(hierarchy_relationships)
        print(f"✅ Extracted {len(df_hierarchy)} hierarchy relationships")

        # Stage 2B: Extract file-class relationships (FIXED)
        print("\n📋 Stage 2B: File-Class Relationships (FIXED)")
        file_class_relationships = extract_file_class_relationships_ast_FIXED()
        df_file_class = pd.DataFrame(file_class_relationships)
        print(f"✅ Extracted {len(df_file_class)} file-class relationships")

        # Combine hierarchy and file-class relationships
        df_combined = pd.concat([df_hierarchy, df_file_class], ignore_index=True)

        # Apply all fixes
        print("\n🔧 Applying all fixes...")

        # Fix 1: Enhanced AST name mapping
        if not df_file_class.empty:
            enhanced_mapping = create_enhanced_ast_name_mapping_FIXED(df_file_class, class_registry)
            df_combined = apply_enhanced_name_correction(df_combined, enhanced_mapping)

        # Fix 2: Standardize variable relationships
        df_combined = standardize_variable_relationships(df_combined)

        # Fix 3: Remove duplicates with enhanced logic
        df_final = remove_duplicates_enhanced_FIXED(df_combined)

        # Fix 4: Memory optimization
        optimize_memory_usage()

        # Fix 5: Upload to Neo4j with enhanced variable handling
        upload_to_neo4j_enhanced(df_final)

        print("\n🎉 FIXED Pipeline completed successfully!")
        print(f"📊 Final dataset: {len(df_final)} relationships")

        return df_final

    except Exception as e:
        print(f"❌ Pipeline failed: {e}")
        raise

print("✅ All critical fixes loaded successfully!")
print("📝 Key improvements:")
print("   🔧 Interface and enum detection in Stage 2B")
print("   🔄 Rate limiting retry logic for LLM processing")
print("   📊 Enhanced variable context handling")
print("   🎯 Case-insensitive AST name mapping")
print("   💾 Memory optimization for large codebases")
print("   🧹 Enhanced duplicate removal")
print("   📈 Improved Neo4j variable display")
print("\n🚀 To run the fixed pipeline:")
print("   df_result = run_fixed_pipeline('/path/to/your/java/project')")
