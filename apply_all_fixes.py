#!/usr/bin/env python3
"""
Apply All Fixes to final_v9.ipynb
This script applies all the critical bug fixes to your pipeline
"""

import json
import re
import shutil
from pathlib import Path

def backup_notebook():
    """Create a backup of the original notebook"""
    original = Path("final_v9.ipynb")
    backup = Path("final_v9_BACKUP.ipynb")
    
    if original.exists():
        shutil.copy2(original, backup)
        print(f"✅ Backup created: {backup}")
        return True
    else:
        print(f"❌ Original file not found: {original}")
        return False

def apply_interface_detection_fix():
    """Apply the interface detection fix to Stage 2B"""
    print("🔧 Applying interface detection fix...")
    
    # Read the notebook
    with open("final_v9.ipynb", "r", encoding="utf-8") as f:
        notebook = json.load(f)
    
    # Find and fix the Stage 2B cell
    for cell in notebook["cells"]:
        if cell["cell_type"] == "code" and "source" in cell:
            source_lines = cell["source"]
            
            # Look for the find_classes function
            for i, line in enumerate(source_lines):
                if "def find_classes(node):" in line:
                    print("   📍 Found find_classes function, applying fix...")
                    
                    # Replace function name
                    source_lines[i] = line.replace("find_classes", "find_classes_and_interfaces")
                    
                    # Find the function body and replace it
                    j = i + 1
                    while j < len(source_lines) and not source_lines[j].strip().startswith("def "):
                        line_content = source_lines[j]
                        
                        # Replace classes = [] with entities = []
                        if "classes = []" in line_content:
                            source_lines[j] = line_content.replace("classes = []", "entities = []")
                        
                        # Add interface and enum detection
                        if "if node.type == 'class_declaration':" in line_content:
                            # Find the end of the class_declaration block
                            k = j + 1
                            indent_level = len(line_content) - len(line_content.lstrip())
                            
                            while k < len(source_lines):
                                next_line = source_lines[k]
                                if next_line.strip() and (len(next_line) - len(next_line.lstrip())) <= indent_level:
                                    break
                                if "classes.append(to_pascal_case(class_name))" in next_line:
                                    source_lines[k] = next_line.replace(
                                        "classes.append(to_pascal_case(class_name))",
                                        "entities.append((to_pascal_case(class_name), 'Class'))"
                                    )
                                k += 1
                            
                            # Insert interface and enum detection after class detection
                            interface_code = [
                                f"{' ' * (indent_level + 4)}elif node.type == 'interface_declaration':\n",
                                f"{' ' * (indent_level + 8)}# Find interface name\n",
                                f"{' ' * (indent_level + 8)}for child in node.children:\n",
                                f"{' ' * (indent_level + 12)}if child.type == 'identifier':\n",
                                f"{' ' * (indent_level + 16)}interface_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n",
                                f"{' ' * (indent_level + 16)}entities.append((to_pascal_case(interface_name), 'Interface'))\n",
                                f"{' ' * (indent_level + 16)}break\n",
                                f"{' ' * (indent_level + 4)}elif node.type == 'enum_declaration':\n",
                                f"{' ' * (indent_level + 8)}# Find enum name\n",
                                f"{' ' * (indent_level + 8)}for child in node.children:\n",
                                f"{' ' * (indent_level + 12)}if child.type == 'identifier':\n",
                                f"{' ' * (indent_level + 16)}enum_name = source_code[child.start_byte:child.end_byte].decode('utf-8')\n",
                                f"{' ' * (indent_level + 16)}entities.append((to_pascal_case(enum_name), 'Enum'))\n",
                                f"{' ' * (indent_level + 16)}break\n"
                            ]
                            
                            # Insert the interface/enum code
                            source_lines[k:k] = interface_code
                            j = k + len(interface_code)
                        
                        # Replace recursive calls
                        if "classes.extend(find_classes(child))" in line_content:
                            source_lines[j] = line_content.replace(
                                "classes.extend(find_classes(child))",
                                "entities.extend(find_classes_and_interfaces(child))"
                            )
                        
                        # Replace return statement
                        if "return classes" in line_content and line_content.strip() == "return classes":
                            source_lines[j] = line_content.replace("return classes", "return entities")
                        
                        j += 1
                    
                    # Fix the function call and loop
                    for k in range(j, len(source_lines)):
                        if "classes_in_file = find_classes(root_node)" in source_lines[k]:
                            source_lines[k] = source_lines[k].replace(
                                "classes_in_file = find_classes(root_node)",
                                "entities_in_file = find_classes_and_interfaces(root_node)"
                            )
                        
                        if "for class_name in classes_in_file:" in source_lines[k]:
                            source_lines[k] = source_lines[k].replace(
                                "for class_name in classes_in_file:",
                                "for entity_name, entity_type in entities_in_file:"
                            )
                        
                        if "'destination_node': class_name," in source_lines[k]:
                            source_lines[k] = source_lines[k].replace(
                                "'destination_node': class_name,",
                                "'destination_node': entity_name,"
                            )
                        
                        if "'destination_type': 'Class'," in source_lines[k]:
                            source_lines[k] = source_lines[k].replace(
                                "'destination_type': 'Class',",
                                "'destination_type': entity_type,"
                            )
                    
                    print("   ✅ Interface detection fix applied")
                    break
    
    # Save the modified notebook
    with open("final_v9.ipynb", "w", encoding="utf-8") as f:
        json.dump(notebook, f, indent=1, ensure_ascii=False)

def add_retry_logic_imports():
    """Add retry logic imports to the notebook"""
    print("🔧 Adding retry logic imports...")
    
    with open("final_v9.ipynb", "r", encoding="utf-8") as f:
        notebook = json.load(f)
    
    # Find the first code cell and add imports
    for cell in notebook["cells"]:
        if cell["cell_type"] == "code" and "source" in cell:
            source_lines = cell["source"]
            
            # Check if imports are already there
            has_tenacity = any("tenacity" in line for line in source_lines)
            
            if not has_tenacity:
                # Add retry imports at the beginning
                retry_imports = [
                    "# FIXED: Add retry logic for rate limiting\n",
                    "import time\n",
                    "from tenacity import retry, stop_after_attempt, wait_exponential\n",
                    "\n"
                ]
                
                source_lines[0:0] = retry_imports
                print("   ✅ Retry logic imports added")
                break
    
    # Save the modified notebook
    with open("final_v9.ipynb", "w", encoding="utf-8") as f:
        json.dump(notebook, f, indent=1, ensure_ascii=False)

def add_retry_decorator():
    """Add retry decorator function"""
    print("🔧 Adding retry decorator function...")
    
    with open("final_v9.ipynb", "r", encoding="utf-8") as f:
        notebook = json.load(f)
    
    # Find a good place to add the retry function (after imports)
    for i, cell in enumerate(notebook["cells"]):
        if cell["cell_type"] == "code" and "source" in cell:
            source_lines = cell["source"]
            
            # Look for LLM initialization or transformer setup
            if "LLMGraphTransformer" in "".join(source_lines):
                # Add retry function after this cell
                retry_cell = {
                    "cell_type": "code",
                    "execution_count": None,
                    "metadata": {},
                    "outputs": [],
                    "source": [
                        "# FIXED: Rate limiting retry logic\n",
                        "@retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=60))\n",
                        "def process_with_retry(transformer, doc):\n",
                        "    \"\"\"Process LLM request with retry logic for rate limiting - FIXED\"\"\"\n",
                        "    try:\n",
                        "        return transformer.convert_to_graph_documents([doc])\n",
                        "    except Exception as e:\n",
                        "        error_str = str(e).lower()\n",
                        "        if \"429\" in error_str or \"rate limit\" in error_str or \"quota\" in error_str:\n",
                        "            print(f\"🔄 Rate limit hit, retrying after delay...\")\n",
                        "            raise  # This will trigger the retry\n",
                        "        else:\n",
                        "            print(f\"❌ Non-rate-limit error: {e}\")\n",
                        "            raise  # Re-raise non-rate-limit errors\n",
                        "\n",
                        "print(\"✅ Rate limiting retry logic added\")\n"
                    ]
                }
                
                notebook["cells"].insert(i + 1, retry_cell)
                print("   ✅ Retry decorator function added")
                break
    
    # Save the modified notebook
    with open("final_v9.ipynb", "w", encoding="utf-8") as f:
        json.dump(notebook, f, indent=1, ensure_ascii=False)

def apply_all_fixes():
    """Apply all critical fixes to the notebook"""
    print("🚀 Applying all critical fixes to final_v9.ipynb...")
    
    # Step 1: Create backup
    if not backup_notebook():
        return False
    
    try:
        # Step 2: Apply interface detection fix
        apply_interface_detection_fix()
        
        # Step 3: Add retry logic
        add_retry_logic_imports()
        add_retry_decorator()
        
        print("\n🎉 All critical fixes applied successfully!")
        print("📝 Fixed issues:")
        print("   ✅ Interface and enum detection in Stage 2B")
        print("   ✅ Rate limiting retry logic added")
        print("   ✅ Enhanced error handling")
        
        print("\n📋 Next steps:")
        print("   1. Review the changes in final_v9.ipynb")
        print("   2. Test with a small Java codebase")
        print("   3. Apply remaining fixes from final_v9_FIXED.py as needed")
        print("   4. Monitor for rate limiting and memory usage")
        
        return True
        
    except Exception as e:
        print(f"❌ Error applying fixes: {e}")
        print("🔄 Restoring backup...")
        
        # Restore backup
        backup = Path("final_v9_BACKUP.ipynb")
        original = Path("final_v9.ipynb")
        if backup.exists():
            shutil.copy2(backup, original)
            print("✅ Backup restored")
        
        return False

if __name__ == "__main__":
    success = apply_all_fixes()
    exit(0 if success else 1)
