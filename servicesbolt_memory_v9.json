{"class_registry": {"DateUtil": {"fqcn": "com.bolt.dashboard.util.DateUtil", "package": "com.bolt.dashboard.util", "file_path": "C:\\Shaik\\sample\\util\\DateUtil.java", "imports": ["java.text.SimpleDateFormat", "java.util.Calendar", "java.util.Date", "java.util.HashMap", "java.util.Map", "org.springframework.stereotype.Component"], "endpoints": [], "db_entities": [], "source_code": "package com.bolt.dashboard.util;\n\nimport java.text.SimpleDateFormat;\nimport java.util.Calendar;\nimport java.util.Date;\nimport java.util.HashMap;\nimport java.util.Map;\n\nimport org.springframework.stereotype.Component;\n\n@Component\npublic class DateUtil {\n\n    public String getDateInFormat(String format, Date dateInput) {\n        String pattern = format;\n        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);\n\n        return simpleDateFormat.format(dateInput);\n\n    }\n\n    public Map<String, String> getLastWeekWorkingDateRange() {\n        Map<String, String> map = new HashMap<>();\n        Date date = new Date();\n        Calendar c = Calendar.getInstance();\n        c.setTime(date);\n        c.setFirstDayOfWeek(2);// set Monday as First day of the week\n        int i = c.get(Calendar.DAY_OF_WEEK) - c.getFirstDayOfWeek();\n        c.add(Calendar.DATE, -i - 7);\n        Date start = c.getTime();\n        c.add(Calendar.DATE, 4);\n        Date end = c.getTime();\n        map.put(\"start\", getDateInFormat(\"yyyy/MM/dd\", start));\n        map.put(\"end\", getDateInFormat(\"yyyy/MM/dd\", end));\n        return map;\n\n    }\n\n}\n"}}, "validated_edges": ["getLastWeekWorkingDateRange-USES-DateUtil.Date", "getDateInFormat-USES-DateUtil.Pattern", "Util-CONTAINS-DateUtil", "getLastWeekWorkingDateRange-USES-DateUtil.End", "getDateInFormat-USES-DateUtil.Format", "DateUtil-DECLARES-getLastWeekWorkingDateRange", "getLastWeekWorkingDateRange-USES-DateUtil.Start", "DateUtil-DECLARES-getDateInFormat", "getDateInFormat-USES-DateUtil.Simpledateformat", "getLastWeekWorkingDateRange-USES-DateUtil.Map"], "method_signatures": {"GetDateInFormat": {"class": "DateUtil", "file_path": "C:\\Shaik\\sample\\util\\DateUtil.java", "stage": "stage_3_registry"}}, "variable_contexts": {}, "variable_flows": {}, "transformation_cache": {}, "stage_2_results": {"relationships": 1, "folders": 0, "files": 1}, "ast_relationships": 12, "ast_name_mapping": {"dateutil": "DateUtil", "getdateinformat": "getDateInFormat", "getlastweekworkingdaterange": "getLastWeekWorkingDateRange"}}